// Disable first-run and onboarding
pref("browser.startup.homepage_override.mstone", "ignore");
pref("browser.startup.homepage_override.buildID", "");
pref("browser.aboutwelcome.enabled", false);
pref("browser.messaging-system.whatsNewPanel.enabled", false);

// Disable Firefox studies and telemetry
pref("app.shield.optoutstudies.enabled", false);
pref("app.normandy.enabled", false);
pref("app.normandy.api_url", "");
pref("toolkit.telemetry.enabled", false);
pref("toolkit.telemetry.unified", false);
pref("toolkit.telemetry.archive.enabled", false);
pref("datareporting.healthreport.uploadEnabled", false);
pref("datareporting.policy.dataSubmissionEnabled", false);

// Disable sponsored suggestions in address bar (Firefox Suggest)
pref("browser.urlbar.suggest.quicksuggest.nonsponsored", false);
pref("browser.urlbar.suggest.quicksuggest.sponsored", false);
pref("browser.urlbar.quicksuggest.enabled", false);

// Disable Pocket and all new tab sponsored stuff
pref("extensions.pocket.enabled", false);
pref("browser.newtabpage.activity-stream.feeds.section.topstories", false);
pref("browser.newtabpage.activity-stream.feeds.snippets", false);
pref("browser.newtabpage.activity-stream.showSponsored", false);
pref("browser.newtabpage.activity-stream.showSponsoredTopSites", false);

// Disable extension recommendations
pref("extensions.htmlaboutaddons.recommendations.enabled", false);
pref("browser.discovery.enabled", false);

// Disable automatic updates
pref("app.update.auto", false);
