# This is a config for E2B sandbox template.
# You can use template ID (k0wmnzir0zuzye6dndlw) or template name (desktop) to create a sandbox:

# Python SDK
# from e2b import Sandbox, AsyncSandbox
# sandbox = Sandbox("desktop") # Sync sandbox
# sandbox = await AsyncSandbox.create("desktop") # Async sandbox

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create('desktop')

team_id = "460355b3-4f64-48f9-9a16-4442817f79f5"
memory_mb = 8_192
cpu_count = 8
dockerfile = "e2b.Dockerfile"
template_name = "desktop"
template_id = "k0wmnzir0zuzye6dndlw"
