<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Use - Sandbox on AWS Demo UI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        .nav-tabs .nav-link {
            color: #495057;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            color: #0d6efd;
            font-weight: 600;
        }
        .coming-soon-badge {
            font-size: 0.65rem;
            margin-left: 5px;
            padding: 0.25em 0.5em;
            vertical-align: middle;
            font-weight: normal;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row header">
            <div class="col-12">
                <h1>Sandbox on AWS Demo UI</h1>
            </div>
        </div>
        
        <!-- Navigation Tabs -->
        <div class="row mb-3">
            <div class="col-12">
                <ul class="nav nav-tabs" id="sandboxTabs">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/browser-use">Browser Use</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/code-interpreter">Code Interpreter</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/computer-use">Computer Use <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-search">AI Search <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-ppt">AI PPT <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="row main-content">
            <!-- Left panel: Desktop stream -->
            <div class="col-md-7 stream-panel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3>Desktop Stream - Power by E2B on AWS</h3>
                        <div id="sandbox-controls" class="d-flex align-items-center" style="display: none;">
                            <span id="sandbox-timer" class="badge bg-secondary me-2 py-2 px-3 fs-6">00:00</span>
                            <span id="sandbox-id" class="badge bg-info me-2"></span>
                            <button id="stop-desktop" class="btn btn-danger py-1 px-2 fs-6">Stop</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="stream-container">
                            <div id="stream-placeholder">
                                <p>No active stream. Start a new desktop to begin.</p>
                            </div>
                            <iframe id="stream-frame" style="display: none;"></iframe>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right panel: Controls and logs -->
            <div class="col-md-5 control-panel">
                <div class="card">
                    <div class="card-header">
                        <h3>Controls</h3>
                    </div>
                    <div class="card-body">
                        <form id="task-form">
                            <div class="mb-3">
                                <label for="task-input" class="form-label">Task Prompt:</label>
                                <textarea id="task-input" class="form-control" rows="4" placeholder="Enter your task here..."></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Example Tasks:</label>
                                <div class="d-flex flex-wrap gap-2 mb-2">
                                    <button type="button" class="btn btn-sm btn-outline-secondary example-task" data-prompt="Search for recent news about artificial intelligence breakthroughs and summarize the top 3 developments.">AI News</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary example-task" data-prompt="Find and compare prices for iPhone 15 Pro Max across major online retailers. Create a comparison table.">Price Compare</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary example-task" data-prompt="Research the most popular tourist destinations in Japan and create an itinerary for a 7-day trip.">Travel Plan</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary example-task" data-prompt="Find the best-rated restaurants in San Francisco with vegetarian options and outdoor seating.">Restaurant Search</button>
                                </div>
                            </div>
                            
                            <div class="d-flex flex-wrap gap-2 mb-4">
                                <button type="button" id="run-workflow" class="btn btn-sm btn-info">Run Task</button>
                            </div>
                            
                            <div class="logs-section mt-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h4>Logs</h4>
                                    <button id="clear-logs" class="btn btn-sm btn-outline-secondary">Clear</button>
                                </div>
                                <div id="log-container">
                                    <div id="logs"></div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/script.js"></script>
    
    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">Image Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" class="img-fluid" alt="Enlarged Image">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
